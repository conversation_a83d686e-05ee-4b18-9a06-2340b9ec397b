#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的程序是否正常工作
"""

import pandas as pd
import numpy as np

def test_modified_program():
    """测试修改后的程序"""
    print("=== 测试修改后的程序 ===\n")
    
    try:
        # 导入修改后的分析器
        from stock_feature_analysis import StockFeatureAnalyzer
        
        print("✓ 成功导入 StockFeatureAnalyzer 类")
        
        # 创建分析器实例
        analyzer = StockFeatureAnalyzer("15di_fantan_2019110120250622.csv")
        
        print("✓ 成功创建分析器实例")
        
        # 检查技术指标数量
        print(f"✓ 技术指标数量：{len(analyzer.feature_columns)} 个")
        
        # 检查新添加的技术指标是否存在
        new_indicators = ['i1_最高_收盘比例', 'i1_成交量比']
        for indicator in new_indicators:
            if indicator in analyzer.feature_columns:
                print(f"✓ 新技术指标 '{indicator}' 已添加")
            else:
                print(f"✗ 新技术指标 '{indicator}' 未找到")
        
        # 检查是否移除了可视化相关的导入
        import stock_feature_analysis
        source_code = open('stock_feature_analysis.py', 'r', encoding='utf-8').read()
        
        if 'matplotlib' not in source_code and 'seaborn' not in source_code:
            print("✓ 已移除 matplotlib 和 seaborn 导入")
        else:
            print("✗ 仍然包含可视化库导入")
        
        if 'create_visualizations' not in source_code:
            print("✓ 已移除可视化方法")
        else:
            print("✗ 仍然包含可视化方法")
        
        if 'plt.' not in source_code:
            print("✓ 已移除所有 plt 调用")
        else:
            print("✗ 仍然包含 plt 调用")
        
        # 测试数据加载（如果文件存在）
        try:
            analyzer.load_and_preprocess_data()
            print("✓ 数据加载功能正常")
            
            # 检查新指标是否在数据中
            for indicator in new_indicators:
                if indicator in analyzer.data.columns:
                    print(f"✓ 数据中包含新指标 '{indicator}'")
                else:
                    print(f"! 数据中不包含新指标 '{indicator}'（这是正常的，如果数据文件中确实没有这些列）")
            
        except FileNotFoundError:
            print("! CSV文件不存在，跳过数据加载测试")
        except Exception as e:
            print(f"✗ 数据加载出错：{e}")
        
        print("\n=== 测试完成 ===")
        
    except ImportError as e:
        print(f"✗ 导入失败：{e}")
    except Exception as e:
        print(f"✗ 测试过程中出现错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_modified_program()
