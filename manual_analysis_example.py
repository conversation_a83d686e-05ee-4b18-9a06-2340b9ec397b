#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动分析示例 - 演示如何分析几个关键技术指标
这个简化版本可以帮助理解分析逻辑
"""

import pandas as pd
import numpy as np

def manual_analysis_example():
    """手动分析示例"""
    print("=== 股票技术指标手动分析示例 ===\n")
    
    try:
        # 加载数据
        print("正在加载数据...")
        try:
            data = pd.read_csv("15di_fantan_2019110120250622.csv", encoding='utf-8')
        except UnicodeDecodeError:
            data = pd.read_csv("15di_fantan_2019110120250622.csv", encoding='gbk')
        
        print(f"数据加载成功：{len(data)} 行，{len(data.columns)} 列")
        
        # 检查目标列
        target_col = '3H_2buy'
        if target_col not in data.columns:
            print(f"错误：找不到目标列 '{target_col}'")
            return
        
        # 清理数据
        data = data.dropna(subset=[target_col])
        print(f"清理后数据：{len(data)} 行")
        
        # 目标列基本统计
        print(f"\n目标列 '{target_col}' 基本统计：")
        print(f"均值：{data[target_col].mean():.4f}")
        print(f"胜率（>0比例）：{(data[target_col] > 0).mean():.4f}")
        print(f"标准差：{data[target_col].std():.4f}")
        print(f"最小值：{data[target_col].min():.4f}")
        print(f"最大值：{data[target_col].max():.4f}")
        
        # 分析几个关键技术指标
        key_indicators = [
            'i2_i1_MA5_ratio',
            'i2_i1_RSI_ratio', 
            'i2_i1_MACD_ratio',
            'i2_i1_KDJ_K_ratio',
            'i2_i1_OBV_ratio'
        ]
        
        print(f"\n=== 分析关键技术指标 ===")
        
        results = []
        
        for indicator in key_indicators:
            if indicator not in data.columns:
                print(f"警告：指标 {indicator} 不存在，跳过")
                continue
            
            print(f"\n分析指标：{indicator}")
            
            # 清理该指标的缺失值
            valid_data = data.dropna(subset=[indicator])
            print(f"有效数据：{len(valid_data)} 行")
            
            if len(valid_data) < 50:
                print("数据量太少，跳过分析")
                continue
            
            # 测试不同阈值
            thresholds = [70, 80, 85, 90, 95]
            best_score = -float('inf')
            best_threshold = None
            best_stats = None
            
            for threshold in thresholds:
                # 计算阈值
                threshold_value = np.percentile(valid_data[indicator], threshold)
                
                # 筛选数据
                filtered_data = valid_data[valid_data[indicator] >= threshold_value]
                
                if len(filtered_data) < 10:  # 样本太少
                    continue
                
                # 计算统计指标
                mean_return = filtered_data[target_col].mean()
                win_rate = (filtered_data[target_col] > 0).mean()
                sample_count = len(filtered_data)
                
                # 综合评分
                composite_score = 0.7 * mean_return + 0.3 * win_rate
                
                print(f"  阈值{threshold}%: 均值={mean_return:.4f}, 胜率={win_rate:.4f}, "
                      f"样本数={sample_count}, 评分={composite_score:.4f}")
                
                if composite_score > best_score:
                    best_score = composite_score
                    best_threshold = threshold
                    best_stats = {
                        'mean_return': mean_return,
                        'win_rate': win_rate,
                        'sample_count': sample_count,
                        'threshold_value': threshold_value
                    }
            
            if best_stats:
                results.append({
                    'indicator': indicator,
                    'best_threshold': best_threshold,
                    'best_score': best_score,
                    **best_stats
                })
                print(f"  最佳阈值：{best_threshold}% (评分={best_score:.4f})")
        
        # 排序结果
        results.sort(key=lambda x: x['best_score'], reverse=True)
        
        print(f"\n=== 分析结果排序 ===")
        print(f"{'排名':<4} {'技术指标':<25} {'最佳阈值%':<10} {'均值':<8} {'胜率':<8} {'样本数':<8} {'评分':<8}")
        print("-" * 80)
        
        for i, result in enumerate(results, 1):
            print(f"{i:<4} {result['indicator']:<25} {result['best_threshold']:<10} "
                  f"{result['mean_return']:<8.4f} {result['win_rate']:<8.4f} "
                  f"{result['sample_count']:<8} {result['best_score']:<8.4f}")
        
        # 简单的效果对比
        if results:
            best_indicator = results[0]
            baseline_mean = data[target_col].mean()
            baseline_win_rate = (data[target_col] > 0).mean()
            
            print(f"\n=== 效果对比 ===")
            print(f"基准表现（全样本）：")
            print(f"  均值：{baseline_mean:.4f}")
            print(f"  胜率：{baseline_win_rate:.4f}")
            
            print(f"\n最佳指标表现（{best_indicator['indicator']}，阈值{best_indicator['best_threshold']}%）：")
            print(f"  均值：{best_indicator['mean_return']:.4f} (提升 {best_indicator['mean_return']-baseline_mean:.4f})")
            print(f"  胜率：{best_indicator['win_rate']:.4f} (提升 {best_indicator['win_rate']-baseline_win_rate:.4f})")
            print(f"  样本数：{best_indicator['sample_count']} (占比 {best_indicator['sample_count']/len(data):.2%})")
        
        print(f"\n分析完成！")
        
    except Exception as e:
        print(f"分析过程中出现错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    manual_analysis_example()
