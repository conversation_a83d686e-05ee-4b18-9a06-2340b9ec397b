#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最佳筛选策略实施代码
基于分析结果的放宽筛选策略（85%阈值）实际应用
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class OptimalStockScreening:
    """最佳股票筛选策略实施类"""
    
    def __init__(self, data_file: str):
        """
        初始化筛选器
        
        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.data = None
        
        # 基于分析结果的最佳筛选条件（85%阈值）
        self.optimal_thresholds = {
            'i1_MA20_ratio': 1.0049,
            'i1_最高_收盘比例': 0.0583,
            'i1_EMA10_ratio': 1.0136,
            'i1_EMA5_ratio': 1.0268,
            'i1_EMA20_ratio': 1.0070
        }
        
        # 预期表现指标
        self.expected_performance = {
            'average_return': 5.74,
            'win_rate': 100.0,
            'sample_count': 7,
            'improvement': 73.0
        }
    
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file, encoding='utf-8')
            print(f"✅ 成功加载数据：{len(self.data)} 行，{len(self.data.columns)} 列")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败：{e}")
            return False
    
    def apply_optimal_screening(self):
        """应用最佳筛选策略"""
        if self.data is None:
            print("❌ 请先加载数据")
            return None
        
        print("\n🔍 应用最佳筛选策略（85%阈值多因子筛选）...")
        
        # 检查必要的列是否存在
        missing_cols = [col for col in self.optimal_thresholds.keys() if col not in self.data.columns]
        if missing_cols:
            print(f"❌ 缺少必要的技术指标列：{missing_cols}")
            return None
        
        # 应用筛选条件
        mask = pd.Series([True] * len(self.data), index=self.data.index)
        
        print("筛选条件：")
        for indicator, threshold in self.optimal_thresholds.items():
            condition = self.data[indicator] >= threshold
            mask = mask & condition
            passed_count = condition.sum()
            print(f"  {indicator} ≥ {threshold:.4f}: {passed_count} 只股票通过")
        
        # 获取筛选结果
        selected_stocks = self.data[mask].copy()
        
        print(f"\n📊 筛选结果：")
        print(f"  通过筛选的股票数量：{len(selected_stocks)} 只")
        print(f"  筛选比例：{len(selected_stocks)/len(self.data)*100:.2f}%")
        
        return selected_stocks
    
    def analyze_selected_stocks(self, selected_stocks):
        """分析筛选出的股票"""
        if selected_stocks is None or len(selected_stocks) == 0:
            print("❌ 没有股票通过筛选")
            return None
        
        print(f"\n📈 筛选股票分析：")
        
        # 基本统计
        target_col = '3H_2buy'
        if target_col in selected_stocks.columns:
            returns = selected_stocks[target_col]
            
            stats = {
                'count': len(returns),
                'mean': returns.mean(),
                'median': returns.median(),
                'std': returns.std(),
                'min': returns.min(),
                'max': returns.max(),
                'win_rate_gt0': (returns > 0).mean() * 100,
                'win_rate_gt1': (returns > 1).mean() * 100,
                'win_rate_gt3': (returns > 3).mean() * 100
            }
            
            print(f"  平均收益率：{stats['mean']:.2f}%")
            print(f"  中位数收益率：{stats['median']:.2f}%")
            print(f"  收益标准差：{stats['std']:.2f}%")
            print(f"  收益范围：{stats['min']:.2f}% ~ {stats['max']:.2f}%")
            print(f"  胜率(>0%)：{stats['win_rate_gt0']:.1f}%")
            print(f"  胜率(>1%)：{stats['win_rate_gt1']:.1f}%")
            print(f"  胜率(>3%)：{stats['win_rate_gt3']:.1f}%")
            
            # 与预期表现对比
            print(f"\n📊 与预期表现对比：")
            print(f"  预期平均收益：{self.expected_performance['average_return']:.2f}% | 实际：{stats['mean']:.2f}%")
            print(f"  预期胜率：{self.expected_performance['win_rate']:.1f}% | 实际：{stats['win_rate_gt0']:.1f}%")
            print(f"  预期样本数：{self.expected_performance['sample_count']} | 实际：{stats['count']}")
            
            return stats
        else:
            print(f"❌ 目标列 '{target_col}' 不存在")
            return None
    
    def generate_portfolio_recommendation(self, selected_stocks):
        """生成投资组合推荐"""
        if selected_stocks is None or len(selected_stocks) == 0:
            return None
        
        print(f"\n💼 投资组合推荐：")
        
        # 如果有股票代码列，显示具体股票
        stock_id_cols = ['secID', 'stock_code', 'code', 'symbol']
        stock_id_col = None
        for col in stock_id_cols:
            if col in selected_stocks.columns:
                stock_id_col = col
                break
        
        if stock_id_col:
            print(f"  推荐股票列表：")
            for i, (idx, row) in enumerate(selected_stocks.iterrows(), 1):
                stock_id = row[stock_id_col]
                expected_return = row['3H_2buy'] if '3H_2buy' in selected_stocks.columns else 'N/A'
                print(f"    {i}. {stock_id} (预期收益: {expected_return}%)")
        
        # 投资组合配置建议
        stock_count = len(selected_stocks)
        equal_weight = 100 / stock_count
        
        print(f"\n  配置建议：")
        print(f"    总股票数：{stock_count} 只")
        print(f"    等权重配置：每只股票 {equal_weight:.1f}%")
        print(f"    建议现金比例：15-20%")
        print(f"    实际股票仓位：80-85%")
        
        # 风险管理建议
        print(f"\n⚠️  风险管理建议：")
        print(f"    单只股票最大权重：20%")
        print(f"    组合止损线：-8%")
        print(f"    单只股票止损线：-5%")
        print(f"    重新筛选频率：每周一次")
        
        return selected_stocks
    
    def run_complete_screening(self):
        """运行完整的筛选流程"""
        print("🚀 开始执行最佳股票筛选策略")
        print("="*50)
        
        # 1. 加载数据
        if not self.load_data():
            return None
        
        # 2. 应用筛选
        selected_stocks = self.apply_optimal_screening()
        
        # 3. 分析结果
        stats = self.analyze_selected_stocks(selected_stocks)
        
        # 4. 生成推荐
        portfolio = self.generate_portfolio_recommendation(selected_stocks)
        
        # 5. 保存结果
        if selected_stocks is not None and len(selected_stocks) > 0:
            output_file = f"最佳筛选结果_{datetime.now().strftime('%Y%m%d')}.csv"
            selected_stocks.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n💾 筛选结果已保存到：{output_file}")
        
        print(f"\n✅ 筛选流程完成！")
        return selected_stocks, stats


def main():
    """主程序"""
    print("最佳股票筛选策略实施程序")
    print("基于85%阈值多因子筛选策略")
    print("="*50)
    
    # 数据文件路径
    data_file = "15di_fantan_2024110120250621.csv"
    
    try:
        # 创建筛选器
        screener = OptimalStockScreening(data_file)
        
        # 运行筛选
        results = screener.run_complete_screening()
        
        if results:
            selected_stocks, stats = results
            
            print(f"\n🎯 策略执行总结：")
            print(f"  筛选策略：85%阈值多因子筛选")
            print(f"  技术指标：5个关键指标")
            print(f"  筛选结果：{len(selected_stocks) if selected_stocks is not None else 0} 只股票")
            
            if stats:
                print(f"  平均收益：{stats['mean']:.2f}%")
                print(f"  胜率：{stats['win_rate_gt0']:.1f}%")
                print(f"  风险收益比：{stats['mean']/stats['std']:.2f}")
            
            print(f"\n💡 下一步行动：")
            print(f"  1. 详细研究筛选出的股票基本面")
            print(f"  2. 根据风险偏好调整仓位配置")
            print(f"  3. 设置止损和监控机制")
            print(f"  4. 定期重新运行筛选程序")
        
    except Exception as e:
        print(f"❌ 程序执行出错：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
