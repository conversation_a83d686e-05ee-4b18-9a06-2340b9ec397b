#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据加载和基本分析
"""

import pandas as pd
import numpy as np

def test_data_loading():
    """测试数据加载"""
    print("正在测试数据加载...")
    
    try:
        # 尝试加载数据
        data = pd.read_csv("15di_fantan_2019110120250622.csv", encoding='utf-8')
        print(f"成功加载数据，共 {len(data)} 行，{len(data.columns)} 列")
    except UnicodeDecodeError:
        try:
            data = pd.read_csv("15di_fantan_2019110120250622.csv", encoding='gbk')
            print(f"使用GBK编码加载数据，共 {len(data)} 行，{len(data.columns)} 列")
        except Exception as e:
            print(f"加载数据失败：{e}")
            return None
    except Exception as e:
        print(f"加载数据失败：{e}")
        return None
    
    # 显示列名
    print("\n前20个列名：")
    for i, col in enumerate(data.columns[:20]):
        print(f"{i+1:2d}. {col}")
    
    # 检查目标列
    target_column = '3H_2buy'
    if target_column in data.columns:
        print(f"\n目标列 '{target_column}' 存在")
        print(f"目标列基本统计：")
        print(data[target_column].describe())
        print(f"目标列大于0的比例：{(data[target_column] > 0).mean():.4f}")
    else:
        print(f"\n警告：目标列 '{target_column}' 不存在")
        print("可用的列名：")
        for col in data.columns:
            if '3H' in col or 'buy' in col:
                print(f"  - {col}")
    
    # 检查技术指标列
    feature_columns = [
        'i2_i1_MA5_ratio', 'i2_i1_MA10_ratio', 'i2_i1_MA20_ratio', 'i2_i1_MA60_ratio',
        'i2_i1_EMA5_ratio', 'i2_i1_EMA10_ratio', 'i2_i1_EMA20_ratio', 'i2_i1_EMA60_ratio'
    ]
    
    print(f"\n检查前8个技术指标列：")
    for col in feature_columns:
        if col in data.columns:
            print(f"  ✓ {col} - 存在")
        else:
            print(f"  ✗ {col} - 不存在")
    
    return data

if __name__ == "__main__":
    test_data_loading()
