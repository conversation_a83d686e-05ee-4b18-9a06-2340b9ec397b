# 股票技术指标特征影响分析程序使用说明

## 程序概述

本程序用于分析57个技术指标对股票"3H_2buy"收益列的影响程度，通过测试不同阈值条件找出最有效的技术指标组合。

## 文件说明

1. **stock_feature_analysis.py** - 主分析程序
2. **test_data_loading.py** - 数据加载测试程序
3. **15di_fantan_2019110120250622.csv** - 数据文件（2622行数据）

## 运行环境要求

```bash
# 需要安装以下Python包
pip install pandas numpy matplotlib seaborn
```

## 使用方法

### 方法1：直接运行主程序
```bash
python stock_feature_analysis.py
```

### 方法2：测试数据加载
```bash
python test_data_loading.py
```

## 分析的技术指标（57个）

程序分析以下技术指标对"3H_2buy"的影响：

**移动平均类指标：**
- i2_i1_MA5_ratio, i2_i1_MA10_ratio, i2_i1_MA20_ratio, i2_i1_MA60_ratio
- i2_i1_EMA5_ratio, i2_i1_EMA10_ratio, i2_i1_EMA20_ratio, i2_i1_EMA60_ratio

**技术振荡指标：**
- i2_i1_RSI_ratio, i2_i1_KDJ_K_ratio, i2_i1_KDJ_D_ratio, i2_i1_KDJ_J_ratio

**成交量指标：**
- i2_i1_OBV_ratio, i2_i1_OBV6_ratio, i2_i1_OBV20_ratio
- i2_i1_VEMA5_ratio, i2_i1_VEMA10_ratio, i2_i1_VEMA12_ratio, i2_i1_VEMA26_ratio

**趋势指标：**
- i2_i1_MACD_ratio, i2_i1_DEA_ratio, i2_i1_DIFF_ratio

**布林带指标：**
- i2_i1_BollUp_ratio, i2_i1_BollDown_ratio

**波动率指标：**
- i2_i1_ATR6_ratio, i2_i1_ATR14_ratio

**心理指标：**
- i2_i1_PSY_ratio, i2_i1_AR_ratio, i2_i1_BR_ratio, i2_i1_VR_ratio

**乖离率指标：**
- i2_i1_BIAS5_ratio, i2_i1_BIAS10_ratio, i2_i1_BIAS20_ratio, i2_i1_BIAS60_ratio

**变化率指标：**
- i2_i1_ROC6_ratio, i2_i1_ROC20_ratio

**其他技术指标：**
- i2_i1_EMV6_ratio, i2_i1_EMV14_ratio, i2_i1_MTM_ratio, i2_i1_MTMMA_ratio
- i2_i1_PVT_ratio, i2_i1_PVT6_ratio, i2_i1_PVT12_ratio
- i2_i1_TRIX5_ratio, i2_i1_TRIX10_ratio, i2_i1_MFI_ratio
- i2_i1_WVAD_ratio, i2_i1_ChaikinOscillator_ratio, i2_i1_ChaikinVolatility_ratio
- i2_i1_ASI_ratio, i2_i1_ARBR_ratio, i2_i1_CR20_ratio, i2_i1_ADTM_ratio
- i2_i1_DDI_ratio, i2_i1_BBI_ratio, i2_i1_UOS_ratio
- i2_i1_MA10RegressCoeff12_ratio, i2_i1_MA10RegressCoeff6_ratio

## 分析方法

### 阈值测试
程序对每个技术指标测试以下百分位数阈值：
- 50%, 60%, 70%, 75%, 80%, 85%, 90%, 95%

### 评估指标
对于每个阈值条件，计算：
1. **均值（平均收益）**：筛选后样本的"3H_2buy"平均值
2. **胜率**：筛选后样本中"3H_2buy">0的比例
3. **综合评分**：0.7×均值 + 0.3×胜率

### 筛选条件
- 只考虑筛选后样本数≥10的情况
- 按综合评分排序找出最佳技术指标

## 输出结果

### 1. 控制台输出
- 数据加载和预处理信息
- 分析进度显示
- 前10名最有效技术指标排名表
- 统计分析结果

### 2. 生成文件
- **technical_indicators_analysis_results.csv** - 详细分析结果
- **technical_indicators_analysis.png** - 可视化图表

### 3. 可视化图表
包含4个子图：
1. 综合评分排名柱状图
2. 均值vs胜率散点图
3. 最佳阈值分布图
4. 样本数量分布直方图

## 预期结果示例

```
技术指标特征影响分析报告
================================================================================

数据概况：
- 总样本数：2621
- 分析的技术指标数量：57
- 目标列 '3H_2buy' 整体均值：1.2345
- 目标列 '3H_2buy' 整体胜率：0.6789

前10名最有效技术指标：
------------------------------------------------------------------------------------------------------------------------
排名 技术指标                              最佳阈值% 均值      胜率      样本数   综合评分
------------------------------------------------------------------------------------------------------------------------
1    <USER>                      <GROUP>        3.4567   0.8234   156      2.6663
2    i2_i1_RSI_ratio                      90        3.1234   0.7890   98       2.4557
3    i2_i1_MACD_ratio                     80        2.9876   0.7654   203      2.3226
...
```

## 注意事项

1. **数据质量**：程序会自动处理缺失值，使用中位数填充
2. **样本数量**：只考虑筛选后样本数≥10的情况，确保统计意义
3. **编码问题**：程序会自动尝试UTF-8和GBK编码加载CSV文件
4. **内存使用**：大数据集可能需要较多内存，建议在配置较好的机器上运行

## 结果解释

- **综合评分越高**：该技术指标在特定阈值下的效果越好
- **最佳阈值**：使该指标效果最优的百分位数阈值
- **均值**：在最佳阈值条件下，"3H_2buy"的平均收益
- **胜率**：在最佳阈值条件下，"3H_2buy">0的比例
- **样本数**：满足阈值条件的样本数量

## 实际应用建议

1. **选择前10名指标**：重点关注综合评分最高的技术指标
2. **组合使用**：可以将多个有效指标组合使用，提高预测准确性
3. **阈值应用**：在实际交易中，当技术指标达到其最佳阈值时，可作为买入信号
4. **定期更新**：建议定期重新分析，因为市场环境会发生变化

## 技术支持

如遇到问题，请检查：
1. Python环境是否正确安装
2. 所需包是否已安装
3. CSV文件是否在正确位置
4. 文件编码是否正确
