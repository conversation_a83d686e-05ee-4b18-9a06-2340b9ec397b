#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票技术指标特征影响分析程序
分析57个技术指标对"3H_2buy"列的影响程度
找出在特定阈值条件下使目标列表现最优的技术指标
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class StockFeatureAnalyzer:
    """股票技术指标特征分析器"""
    
    def __init__(self, csv_file_path: str):
        """
        初始化分析器
        
        Args:
            csv_file_path: CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.data = None
        self.target_column = '3H_2buy'
        
        # 57个技术指标列名
        self.feature_columns = [
            'i2_i1_MA5_ratio', 'i2_i1_MA10_ratio', 'i2_i1_MA20_ratio', 'i2_i1_MA60_ratio',
            'i2_i1_EMA5_ratio', 'i2_i1_EMA10_ratio', 'i2_i1_EMA20_ratio', 'i2_i1_EMA60_ratio',
            'i2_i1_RSI_ratio', 'i2_i1_KDJ_K_ratio', 'i2_i1_KDJ_D_ratio', 'i2_i1_KDJ_J_ratio',
            'i2_i1_OBV_ratio', 'i2_i1_OBV6_ratio', 'i2_i1_OBV20_ratio', 'i2_i1_MACD_ratio',
            'i2_i1_VEMA5_ratio', 'i2_i1_VEMA10_ratio', 'i2_i1_VEMA12_ratio', 'i2_i1_VEMA26_ratio',
            'i2_i1_BollUp_ratio', 'i2_i1_BollDown_ratio', 'i2_i1_ATR6_ratio', 'i2_i1_ATR14_ratio',
            'i2_i1_PSY_ratio', 'i2_i1_AR_ratio', 'i2_i1_BR_ratio', 'i2_i1_VR_ratio',
            'i2_i1_BIAS5_ratio', 'i2_i1_BIAS10_ratio', 'i2_i1_BIAS20_ratio', 'i2_i1_BIAS60_ratio',
            'i2_i1_ROC6_ratio', 'i2_i1_ROC20_ratio', 'i2_i1_EMV6_ratio', 'i2_i1_EMV14_ratio',
            'i2_i1_MTM_ratio', 'i2_i1_MTMMA_ratio', 'i2_i1_PVT_ratio', 'i2_i1_PVT6_ratio',
            'i2_i1_PVT12_ratio', 'i2_i1_TRIX5_ratio', 'i2_i1_TRIX10_ratio', 'i2_i1_MFI_ratio',
            'i2_i1_WVAD_ratio', 'i2_i1_ChaikinOscillator_ratio', 'i2_i1_ChaikinVolatility_ratio',
            'i2_i1_ASI_ratio', 'i2_i1_ARBR_ratio', 'i2_i1_CR20_ratio', 'i2_i1_ADTM_ratio',
            'i2_i1_DDI_ratio', 'i2_i1_DEA_ratio', 'i2_i1_DIFF_ratio', 'i2_i1_BBI_ratio',
            'i2_i1_UOS_ratio', 'i2_i1_MA10RegressCoeff12_ratio', 'i2_i1_MA10RegressCoeff6_ratio'
        ]
        
        # 测试的百分位数阈值
        self.percentile_thresholds = [50, 60, 70, 75, 80, 85, 90, 95]
        
        # 存储分析结果
        self.analysis_results = {}
        
    def load_and_preprocess_data(self) -> None:
        """加载和预处理数据"""
        print("正在加载数据...")
        
        # 加载CSV文件
        try:
            self.data = pd.read_csv(self.csv_file_path, encoding='utf-8')
            print(f"成功加载数据，共 {len(self.data)} 行，{len(self.data.columns)} 列")
        except UnicodeDecodeError:
            self.data = pd.read_csv(self.csv_file_path, encoding='gbk')
            print(f"使用GBK编码加载数据，共 {len(self.data)} 行，{len(self.data.columns)} 列")
        
        # 检查目标列是否存在
        if self.target_column not in self.data.columns:
            raise ValueError(f"目标列 '{self.target_column}' 不存在于数据中")
        
        # 检查技术指标列是否存在
        missing_features = [col for col in self.feature_columns if col not in self.data.columns]
        if missing_features:
            print(f"警告：以下技术指标列不存在于数据中：{missing_features}")
            self.feature_columns = [col for col in self.feature_columns if col in self.data.columns]
        
        print(f"实际可用的技术指标列数：{len(self.feature_columns)}")
        
        # 数据质量检查
        print("\n数据质量检查：")
        print(f"目标列 '{self.target_column}' 缺失值数量：{self.data[self.target_column].isnull().sum()}")
        
        # 处理缺失值
        initial_rows = len(self.data)
        self.data = self.data.dropna(subset=[self.target_column])
        print(f"删除目标列缺失值后剩余 {len(self.data)} 行数据")
        
        # 检查技术指标列的缺失值情况
        feature_missing = self.data[self.feature_columns].isnull().sum()
        if feature_missing.sum() > 0:
            print("技术指标列缺失值情况：")
            for col, missing_count in feature_missing[feature_missing > 0].items():
                print(f"  {col}: {missing_count} 个缺失值")
        
        # 填充技术指标列的缺失值（使用中位数）
        for col in self.feature_columns:
            if self.data[col].isnull().sum() > 0:
                median_val = self.data[col].median()
                self.data[col].fillna(median_val, inplace=True)
        
        # 显示目标列的基本统计信息
        print(f"\n目标列 '{self.target_column}' 基本统计：")
        print(self.data[self.target_column].describe())
        
        print(f"目标列大于0的比例：{(self.data[self.target_column] > 0).mean():.4f}")
        
        print("数据预处理完成！")
        
    def analyze_feature_impact(self, feature_col: str, threshold_percentile: float) -> Dict[str, float]:
        """
        分析单个特征在指定阈值下的影响
        
        Args:
            feature_col: 特征列名
            threshold_percentile: 百分位数阈值
            
        Returns:
            包含均值、胜率、样本数的字典
        """
        # 计算阈值
        threshold_value = np.percentile(self.data[feature_col], threshold_percentile)
        
        # 筛选满足阈值条件的数据
        filtered_data = self.data[self.data[feature_col] >= threshold_value]
        
        if len(filtered_data) == 0:
            return {
                'mean_return': 0.0,
                'win_rate': 0.0,
                'sample_count': 0,
                'threshold_value': threshold_value
            }
        
        # 计算目标列的均值和胜率
        target_values = filtered_data[self.target_column]
        mean_return = target_values.mean()
        win_rate = (target_values > 0).mean()
        sample_count = len(filtered_data)
        
        return {
            'mean_return': mean_return,
            'win_rate': win_rate,
            'sample_count': sample_count,
            'threshold_value': threshold_value
        }

    def batch_analyze_features(self) -> None:
        """批量分析所有技术指标在不同阈值下的影响"""
        print("开始批量分析技术指标...")

        total_features = len(self.feature_columns)
        total_thresholds = len(self.percentile_thresholds)

        for i, feature in enumerate(self.feature_columns):
            print(f"正在分析 {feature} ({i+1}/{total_features})")

            feature_results = {}

            for threshold in self.percentile_thresholds:
                try:
                    result = self.analyze_feature_impact(feature, threshold)
                    feature_results[threshold] = result
                except Exception as e:
                    print(f"  警告：分析 {feature} 在阈值 {threshold}% 时出错：{e}")
                    feature_results[threshold] = {
                        'mean_return': 0.0,
                        'win_rate': 0.0,
                        'sample_count': 0,
                        'threshold_value': 0.0
                    }

            self.analysis_results[feature] = feature_results

        print("批量分析完成！")

    def find_best_thresholds(self) -> pd.DataFrame:
        """找出每个技术指标的最佳阈值组合"""
        print("正在寻找最佳阈值组合...")

        best_results = []

        for feature, thresholds_data in self.analysis_results.items():
            best_score = -float('inf')
            best_threshold = None
            best_data = None

            for threshold, data in thresholds_data.items():
                # 只考虑样本数足够的情况（至少10个样本）
                if data['sample_count'] < 10:
                    continue

                # 计算综合评分：均值和胜率的加权平均
                # 给均值更高的权重，因为它直接反映收益
                composite_score = 0.7 * data['mean_return'] + 0.3 * data['win_rate']

                if composite_score > best_score:
                    best_score = composite_score
                    best_threshold = threshold
                    best_data = data.copy()

            if best_data is not None:
                best_results.append({
                    'feature': feature,
                    'best_threshold_percentile': best_threshold,
                    'threshold_value': best_data['threshold_value'],
                    'mean_return': best_data['mean_return'],
                    'win_rate': best_data['win_rate'],
                    'sample_count': best_data['sample_count'],
                    'composite_score': best_score
                })

        # 转换为DataFrame并按综合评分排序
        results_df = pd.DataFrame(best_results)
        results_df = results_df.sort_values('composite_score', ascending=False).reset_index(drop=True)

        print(f"找到 {len(results_df)} 个有效的技术指标最佳阈值组合")
        return results_df

    def create_visualizations(self, results_df: pd.DataFrame, top_n: int = 10) -> None:
        """创建可视化图表"""
        print(f"正在创建前 {top_n} 个技术指标的可视化图表...")

        # 取前N个最佳指标
        top_results = results_df.head(top_n)

        # 设置图表样式
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('技术指标特征影响分析结果', fontsize=16, fontweight='bold')

        # 1. 综合评分柱状图
        ax1 = axes[0, 0]
        bars1 = ax1.bar(range(len(top_results)), top_results['composite_score'],
                       color='steelblue', alpha=0.7)
        ax1.set_title('综合评分排名（前10名）', fontweight='bold')
        ax1.set_xlabel('技术指标排名')
        ax1.set_ylabel('综合评分')
        ax1.set_xticks(range(len(top_results)))
        ax1.set_xticklabels([f'#{i+1}' for i in range(len(top_results))])

        # 添加数值标签
        for i, bar in enumerate(bars1):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{height:.4f}', ha='center', va='bottom', fontsize=8)

        # 2. 均值vs胜率散点图
        ax2 = axes[0, 1]
        scatter = ax2.scatter(top_results['win_rate'], top_results['mean_return'],
                            c=top_results['composite_score'], cmap='viridis',
                            s=100, alpha=0.7)
        ax2.set_title('均值 vs 胜率分布', fontweight='bold')
        ax2.set_xlabel('胜率')
        ax2.set_ylabel('平均收益')
        plt.colorbar(scatter, ax=ax2, label='综合评分')

        # 添加指标名称标注
        for i, row in top_results.iterrows():
            ax2.annotate(f'#{i+1}', (row['win_rate'], row['mean_return']),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)

        # 3. 最佳阈值分布
        ax3 = axes[1, 0]
        threshold_counts = top_results['best_threshold_percentile'].value_counts().sort_index()
        bars3 = ax3.bar(threshold_counts.index, threshold_counts.values,
                       color='lightcoral', alpha=0.7)
        ax3.set_title('最佳阈值百分位数分布', fontweight='bold')
        ax3.set_xlabel('阈值百分位数 (%)')
        ax3.set_ylabel('指标数量')

        # 添加数值标签
        for bar in bars3:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{int(height)}', ha='center', va='bottom')

        # 4. 样本数量分布
        ax4 = axes[1, 1]
        ax4.hist(top_results['sample_count'], bins=10, color='lightgreen', alpha=0.7, edgecolor='black')
        ax4.set_title('筛选后样本数量分布', fontweight='bold')
        ax4.set_xlabel('样本数量')
        ax4.set_ylabel('指标数量')

        plt.tight_layout()
        plt.savefig('technical_indicators_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("可视化图表已保存为 'technical_indicators_analysis.png'")

    def generate_detailed_report(self, results_df: pd.DataFrame) -> None:
        """生成详细的分析报告"""
        print("\n" + "="*80)
        print("技术指标特征影响分析报告")
        print("="*80)

        print(f"\n数据概况：")
        print(f"- 总样本数：{len(self.data)}")
        print(f"- 分析的技术指标数量：{len(self.feature_columns)}")
        print(f"- 目标列 '{self.target_column}' 整体均值：{self.data[self.target_column].mean():.4f}")
        print(f"- 目标列 '{self.target_column}' 整体胜率：{(self.data[self.target_column] > 0).mean():.4f}")

        print(f"\n前10名最有效技术指标：")
        print("-" * 120)
        print(f"{'排名':<4} {'技术指标':<35} {'最佳阈值%':<10} {'均值':<10} {'胜率':<10} {'样本数':<8} {'综合评分':<10}")
        print("-" * 120)

        for i, row in results_df.head(10).iterrows():
            print(f"{i+1:<4} {row['feature']:<35} {row['best_threshold_percentile']:<10.0f} "
                  f"{row['mean_return']:<10.4f} {row['win_rate']:<10.4f} "
                  f"{row['sample_count']:<8.0f} {row['composite_score']:<10.4f}")

        # 统计分析
        print(f"\n统计分析：")
        print(f"- 平均最佳阈值百分位数：{results_df['best_threshold_percentile'].mean():.1f}%")
        print(f"- 最常用的阈值百分位数：{results_df['best_threshold_percentile'].mode().iloc[0]:.0f}%")
        print(f"- 前10名指标平均收益提升：{(results_df.head(10)['mean_return'].mean() - self.data[self.target_column].mean()):.4f}")
        print(f"- 前10名指标平均胜率提升：{(results_df.head(10)['win_rate'].mean() - (self.data[self.target_column] > 0).mean()):.4f}")

        # 保存详细结果到CSV
        results_df.to_csv('technical_indicators_analysis_results.csv', index=False, encoding='utf-8-sig')
        print(f"\n详细结果已保存到 'technical_indicators_analysis_results.csv'")

        print("\n分析完成！")

    def run_complete_analysis(self) -> pd.DataFrame:
        """运行完整的分析流程"""
        print("开始完整的技术指标特征影响分析...")

        # 1. 数据加载和预处理
        self.load_and_preprocess_data()

        # 2. 批量分析
        self.batch_analyze_features()

        # 3. 找出最佳阈值
        results_df = self.find_best_thresholds()

        # 4. 创建可视化
        self.create_visualizations(results_df)

        # 5. 生成报告
        self.generate_detailed_report(results_df)

        return results_df


def main():
    """主程序入口"""
    # CSV文件路径
    csv_file_path = "15di_fantan_2019110120250622.csv"

    try:
        # 创建分析器实例
        analyzer = StockFeatureAnalyzer(csv_file_path)

        # 运行完整分析
        results = analyzer.run_complete_analysis()

        print(f"\n分析成功完成！共分析了 {len(results)} 个技术指标。")

        # 显示最佳指标的简要信息
        if len(results) > 0:
            best_indicator = results.iloc[0]
            print(f"\n最佳技术指标：{best_indicator['feature']}")
            print(f"最佳阈值：{best_indicator['best_threshold_percentile']:.0f}% (值: {best_indicator['threshold_value']:.4f})")
            print(f"在此条件下的平均收益：{best_indicator['mean_return']:.4f}")
            print(f"在此条件下的胜率：{best_indicator['win_rate']:.4f}")
            print(f"筛选后的样本数：{best_indicator['sample_count']:.0f}")

    except FileNotFoundError:
        print(f"错误：找不到文件 '{csv_file_path}'")
        print("请确保CSV文件在当前目录下")
    except Exception as e:
        print(f"分析过程中出现错误：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
